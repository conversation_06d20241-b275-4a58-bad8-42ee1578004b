import re
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
import logging
import datetime
from typing import Dict, List, Any

from src.config import EMAIL_ADDRESS, EMAIL_PASSWORD, SYSTEM_ADMIN_ID

logger = logging.getLogger()

def is_admin(user_id):
    """Return True if the user is the admin."""
    return int(user_id) == SYSTEM_ADMIN_ID

def send_email_notification(email_address, subject, message, image_path=None):
    """
    Sends email notification with optional image attachment
    """
    try:
        # Create email message
        msg = MIMEMultipart()
        msg["From"] = EMAIL_ADDRESS
        msg["To"] = email_address
        msg["Subject"] = subject

        # Add text to email
        msg.attach(MIMEText(message, "html"))

        # Add image if provided
        if image_path:
            try:
                with open(image_path, "rb") as img_file:
                    img = MIMEImage(img_file.read())
                    img.add_header(
                        "Content-Disposition", "attachment", filename="receipt.jpg"
                    )
                    msg.attach(img)
            except Exception as e:
                logger.error(f"Error attaching image: {str(e)}")

        # Connect to Gmail SMTP server
        server = smtplib.SMTP("smtp.gmail.com", 587)
        server.starttls()

        # Login with application password
        server.login(EMAIL_ADDRESS, EMAIL_PASSWORD)

        # Send email
        server.send_message(msg)
        server.quit()

        return True
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        return False


def format_order_summary(order, user_info):
    """
    Formats order information into a nice HTML summary for email notifications
    """
    # Get order details
    restaurant_name = order.get(
        "restaurant", order.get("restaurant_name", "Unknown Restaurant")
    )
    items = order.get("items", [])
    delivery_fee = order.get("delivery_fee", 0)
    subtotal = order.get("subtotal", 0)
    total_price = subtotal + delivery_fee
    order_number = order.get("order_number", "Unknown")

    # Get delivery details
    delivery_name = order.get("delivery_name", "Not specified")
    delivery_location = order.get(
        "delivery_location", order.get("delivery_gate", "Not specified")
    )
    phone_number = order.get("phone_number", "Not specified")

    # Get order description/special instructions
    order_description = order.get("order_description", "No special instructions")

    # Get restaurant area
    restaurant_area = order.get("restaurant_area", "Not specified")

    # Get payment details
    payment_method = order.get("payment_method", "Not specified")
    points_used = order.get("points_used", 0)

    # Get timestamp and estimated delivery
    order_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    delivery_time_min = (
        datetime.datetime.now() + datetime.timedelta(minutes=30)
    ).strftime("%H:%M")
    delivery_time_max = (
        datetime.datetime.now() + datetime.timedelta(minutes=45)
    ).strftime("%H:%M")
    estimated_delivery = f"{delivery_time_min} - {delivery_time_max}"

    # Get user info
    name = user_info.get("full_name", user_info.get("name", "Unknown Customer"))

    # Group identical items and count them
    item_counts = {}
    for item in items:
        item_name = item.get("name", "Unknown Item")
        item_price = item.get("price", 0)

        # If this item name already exists, increment its count
        if item_name in item_counts:
            item_counts[item_name]["quantity"] += 1
        else:
            # Otherwise, add it to the dictionary
            item_counts[item_name] = {"price": item_price, "quantity": 1}

    # Create HTML for consolidated items
    items_html = ""
    for item_name, details in item_counts.items():
        quantity = details["quantity"]
        price = details["price"]
        total_item_price = price * quantity
        items_html += f"<tr><td>{item_name}</td><td>{quantity}</td><td>{price} Birr</td><td>{total_item_price} Birr</td></tr>"

    # Points information
    points_info = ""
    if points_used > 0:
        points_info = f"""
        <div style="background-color: #f9f9f9; padding: 10px; border-radius: 5px; margin: 15px 0;">
            <p style="font-weight: bold; color: #4CAF50;">Points Used: {points_used} points</p>
            <p>Delivery fee has been covered by your points!</p>
        </div>
        """

    # Create full HTML
    html = f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; }}
            .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
            h1 {{ color: #4CAF50; }}
            h2 {{ color: #4CAF50; background-color: #f5f5f5; padding: 8px; border-radius: 4px; }}
            table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
            th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
            th {{ background-color: #f2f2f2; }}
            .total {{ font-weight: bold; margin-top: 20px; }}
            .highlight {{ background-color: #f9fff9; padding: 10px; border-radius: 5px; }}
            .info-box {{ background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Order Confirmation</h1>
            <p>Dear {name},</p>
            <p>Thank you for your order! Below are your order details:</p>

            <div class="info-box">
                <h2>Order Information</h2>
                <p><strong>Order Number:</strong> {order_number}</p>
                <p><strong>Order Time:</strong> {order_time}</p>
                <p><strong>Estimated Delivery:</strong> {estimated_delivery}</p>
                <p><strong>Special Instructions:</strong> {order_description}</p>
            </div>

            <div class="info-box">
                <h2>Restaurant Details</h2>
                <p><strong>Restaurant:</strong> {restaurant_name}</p>
                <p><strong>Area:</strong> {restaurant_area}</p>
            </div>

            <div class="info-box">
                <h2>Delivery Details</h2>
                <p><strong>Recipient Name:</strong> {delivery_name}</p>
                <p><strong>Delivery Location:</strong> {delivery_location}</p>
                <p><strong>Contact Phone:</strong> {phone_number}</p>
            </div>

            {points_info}

            <h2>Order Items</h2>
            <table>
                <tr>
                    <th>Item</th>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th>Total Price</th>
                </tr>
                {items_html}
            </table>

            <div class="highlight">
                <p class="total">Subtotal: {subtotal} Birr</p>
                <p class="total">Delivery Fee: {delivery_fee} Birr {' (Covered by points)' if points_used > 0 else ''}</p>
                <p class="total" style="font-size: 18px; color: #4CAF50;">Total: {total_price - points_used} Birr</p>
                <p><strong>Payment Method:</strong> {payment_method}</p>
            </div>

            <p>Your order is being processed and will be delivered soon.</p>
            <p>For any questions, please reply to this email or contact us on Telegram.</p>

            <p>Thank you for choosing Wiz Aroma Food Delivery!</p>
        </div>
    </body>
    </html>
    """

    return html


def is_valid_phone(phone):
    """
    Validates if the given string is a valid Ethiopian phone number
    Accepts formats:
    - 09xxxxxxxx
    - 9xxxxxxxx
    - +2519xxxxxxxx
    - 2519xxxxxxxx
    """
    # Remove any spaces, dashes or other separators
    phone = re.sub(r"[\s\-\(\)]", "", phone)

    # Check different valid formats
    ethiopian_patterns = [
        r"^09\d{8}$",  # 09xxxxxxxx
        r"^9\d{8}$",  # 9xxxxxxxx
        r"^\+2519\d{8}$",  # +2519xxxxxxxx
        r"^2519\d{8}$",  # 2519xxxxxxxx
    ]

    for pattern in ethiopian_patterns:
        if re.match(pattern, phone):
            return True

    return False


def consolidate_order_items(items: List[Dict[str, Any]]) -> str:
    """
    Consolidate duplicate order items into a formatted string.
    Returns items in the format: "• Item Name (x3) - 540 birr"

    Args:
        items: List of order items with 'name', 'price', and optionally 'quantity'

    Returns:
        Formatted string with consolidated items
    """
    if not items:
        return "- No items found"

    # Group identical items
    item_counts = {}
    for item in items:
        item_name = item.get("name", "Unknown item")
        item_price = item.get("price", 0)
        item_quantity = item.get("quantity", 1)

        if item_name in item_counts:
            item_counts[item_name]["quantity"] += item_quantity
            item_counts[item_name]["total"] += item_price * item_quantity
        else:
            item_counts[item_name] = {
                "price": item_price,
                "quantity": item_quantity,
                "total": item_price * item_quantity,
            }

    # Format consolidated items
    items_text = ""
    for item_name, details in item_counts.items():
        if details["quantity"] > 1:
            items_text += f"• {item_name} (x{details['quantity']}) - {details['total']} birr\n"
        else:
            items_text += f"• {item_name} - {details['price']} birr\n"

    return items_text.strip()
