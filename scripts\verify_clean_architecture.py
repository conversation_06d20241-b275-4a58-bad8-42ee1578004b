#!/usr/bin/env python3
"""
Verification Script for Wiz-Aroma Clean Architecture
Tests that the system properly separates temporary business data from persistent configuration
and that cleanup mechanisms work correctly.
"""

import os
import sys
import time
import json
from typing import Dict, Any, List

# Add parent directory to path for imports (since we're in scripts/ subdirectory)
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

def test_data_model_structure():
    """Test that data models follow the new architecture"""
    print("🔍 Testing data model structure...")
    
    try:
        # Import data models
        from src.data_models import (
            # Temporary business data
            temp_orders, temp_order_status, temp_pending_admin_reviews,
            temp_admin_remarks, temp_awaiting_receipt, temp_delivery_locations,
            temp_current_order_numbers, temp_delivery_personnel_assignments,
            temp_delivery_personnel_availability, temp_delivery_personnel_capacity,
            
            # Configuration data
            config_areas_data, config_restaurants_data, config_menus_data,
            config_delivery_locations_data, config_delivery_fees_data,
            
            # Persistent user data
            user_points, user_names, user_phone_numbers, user_emails,
            user_order_history, favorite_orders, delivery_personnel,
            delivery_personnel_earnings
        )
        
        # Test that temporary data structures are empty initially
        temp_data_structures = [
            (temp_orders, "temp_orders"),
            (temp_order_status, "temp_order_status"),
            (temp_pending_admin_reviews, "temp_pending_admin_reviews"),
            (temp_admin_remarks, "temp_admin_remarks"),
            (temp_awaiting_receipt, "temp_awaiting_receipt"),
            (temp_delivery_locations, "temp_delivery_locations"),
            (temp_current_order_numbers, "temp_current_order_numbers"),
            (temp_delivery_personnel_assignments, "temp_delivery_personnel_assignments"),
            (temp_delivery_personnel_availability, "temp_delivery_personnel_availability"),
            (temp_delivery_personnel_capacity, "temp_delivery_personnel_capacity")
        ]
        
        for data_dict, name in temp_data_structures:
            if not isinstance(data_dict, dict):
                print(f"❌ {name} is not a dictionary")
                return False
            print(f"✅ {name} is properly structured as dictionary")
        
        print("✅ Data model structure test passed")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Data model structure test failed: {e}")
        return False

def test_data_management_patterns():
    """Test data management patterns and naming conventions"""
    print("\n🔍 Testing data management patterns...")
    
    try:
        from src.utils.data_management_patterns import (
            DataManagementPatterns, DataCategory,
            validate_data_operation, get_all_data_documentation
        )
        
        patterns = DataManagementPatterns()
        
        # Test naming conventions
        temp_name = patterns.get_temp_data_name("orders")
        if temp_name != "temp_orders":
            print(f"❌ Expected 'temp_orders', got '{temp_name}'")
            return False
        
        config_name = patterns.get_config_data_name("areas")
        if config_name != "config_areas":
            print(f"❌ Expected 'config_areas', got '{config_name}'")
            return False
        
        # Test data categorization
        temp_category = patterns.get_data_lifecycle_category("temp_orders")
        if temp_category != DataCategory.TEMPORARY_BUSINESS:
            print(f"❌ Expected TEMPORARY_BUSINESS, got {temp_category}")
            return False
        
        config_category = patterns.get_data_lifecycle_category("config_areas_data")
        if config_category != DataCategory.STATIC_CONFIG:
            print(f"❌ Expected STATIC_CONFIG, got {config_category}")
            return False
        
        # Test validation
        if not validate_data_operation("temp_orders", "add"):
            print("❌ Valid operation rejected")
            return False
        
        if validate_data_operation("temp_orders", "save_to_file"):
            print("❌ Invalid operation accepted")
            return False
        
        print("✅ Data management patterns test passed")
        return True
        
    except Exception as e:
        print(f"❌ Data management patterns test failed: {e}")
        return False

def test_temporary_data_cleanup():
    """Test temporary data cleanup mechanisms"""
    print("\n🔍 Testing temporary data cleanup...")
    
    try:
        from src.utils.temp_data_manager import (
            cleanup_business_data_after_order_completion,
            cleanup_user_session_data,
            force_cleanup_all_temporary_business_data
        )
        from src.data_models import temp_orders, temp_order_status
        
        # Add some test data
        test_user_id = 12345
        test_order_number = "test_order_123"
        
        temp_orders[str(test_user_id)] = {"test": "data"}
        temp_order_status[str(test_user_id)] = "TESTING"
        
        # Test user session cleanup
        cleanup_success = cleanup_user_session_data(test_user_id)
        if not cleanup_success:
            print("❌ User session cleanup failed")
            return False
        
        # Verify data was cleaned
        if str(test_user_id) in temp_orders or str(test_user_id) in temp_order_status:
            print("❌ User session data not properly cleaned")
            return False
        
        # Test force cleanup
        temp_orders["test"] = {"data": "test"}
        cleaned_count = force_cleanup_all_temporary_business_data()
        
        if len(temp_orders) > 0:
            print("❌ Force cleanup did not clear all data")
            return False
        
        print("✅ Temporary data cleanup test passed")
        return True
        
    except Exception as e:
        print(f"❌ Temporary data cleanup test failed: {e}")
        return False

def test_firebase_business_data_manager():
    """Test Firebase business data manager"""
    print("\n🔍 Testing Firebase business data manager...")
    
    try:
        from src.utils.firebase_business_data import get_firebase_business_data_manager
        
        manager = get_firebase_business_data_manager()
        
        # Test that manager exists and has required methods
        required_methods = [
            'create_order', 'update_order_status', 'complete_order',
            'cancel_order', 'add_pending_admin_review', 'remove_pending_admin_review'
        ]
        
        for method_name in required_methods:
            if not hasattr(manager, method_name):
                print(f"❌ Manager missing method: {method_name}")
                return False
            if not callable(getattr(manager, method_name)):
                print(f"❌ Manager method not callable: {method_name}")
                return False
        
        print("✅ Firebase business data manager test passed")
        return True
        
    except Exception as e:
        print(f"❌ Firebase business data manager test failed: {e}")
        return False

def test_auto_cleanup_service():
    """Test automatic cleanup service"""
    print("\n🔍 Testing automatic cleanup service...")
    
    try:
        from src.utils.auto_cleanup_service import get_auto_cleanup_service
        
        service = get_auto_cleanup_service()
        
        # Test that service exists and has required methods
        required_methods = [
            'start_service', 'stop_service', 'get_cleanup_stats',
            'manual_cleanup_order', 'manual_cleanup_user_session'
        ]
        
        for method_name in required_methods:
            if not hasattr(service, method_name):
                print(f"❌ Service missing method: {method_name}")
                return False
            if not callable(getattr(service, method_name)):
                print(f"❌ Service method not callable: {method_name}")
                return False
        
        # Test cleanup stats
        stats = service.get_cleanup_stats()
        if not isinstance(stats, dict):
            print("❌ Cleanup stats should be a dictionary")
            return False
        
        print("✅ Automatic cleanup service test passed")
        return True
        
    except Exception as e:
        print(f"❌ Automatic cleanup service test failed: {e}")
        return False

def test_legacy_compatibility():
    """Test that legacy code still works with new architecture"""
    print("\n🔍 Testing legacy compatibility...")
    
    try:
        # Test that old imports still work
        from src.data_models import orders, order_status, pending_admin_reviews
        from src.data_models import areas_data, restaurants_data, menus_data
        
        # Test that aliases point to correct new structures
        from src.data_models import temp_orders, temp_order_status, temp_pending_admin_reviews
        from src.data_models import config_areas_data, config_restaurants_data, config_menus_data
        
        # Verify aliases work
        if orders is not temp_orders:
            print("❌ orders alias not pointing to temp_orders")
            return False
        
        if order_status is not temp_order_status:
            print("❌ order_status alias not pointing to temp_order_status")
            return False
        
        if areas_data is not config_areas_data:
            print("❌ areas_data alias not pointing to config_areas_data")
            return False
        
        print("✅ Legacy compatibility test passed")
        return True
        
    except Exception as e:
        print(f"❌ Legacy compatibility test failed: {e}")
        return False

def test_no_permanent_local_storage():
    """Test that no business data is permanently stored locally"""
    print("\n🔍 Testing no permanent local storage for business data...")
    
    try:
        # Check that deprecated file operations are properly marked
        from src.data_storage import _safe_write_json, initialize_data_files
        
        # Test that deprecated functions don't actually write files
        test_data = {"test": "data"}
        result = _safe_write_json("test_file.json", test_data)
        
        # Should return True (for compatibility) but not actually write file
        if not result:
            print("❌ _safe_write_json should return True for compatibility")
            return False
        
        # Check that test file was not actually created
        if os.path.exists("test_file.json"):
            print("❌ _safe_write_json actually wrote a file (should be no-op)")
            os.remove("test_file.json")  # Clean up
            return False
        
        print("✅ No permanent local storage test passed")
        return True
        
    except Exception as e:
        print(f"❌ No permanent local storage test failed: {e}")
        return False

def main():
    """Run all verification tests"""
    print("🚀 Starting Wiz-Aroma Clean Architecture Verification\n")
    
    tests = [
        ("Data Model Structure", test_data_model_structure),
        ("Data Management Patterns", test_data_management_patterns),
        ("Temporary Data Cleanup", test_temporary_data_cleanup),
        ("Firebase Business Data Manager", test_firebase_business_data_manager),
        ("Auto Cleanup Service", test_auto_cleanup_service),
        ("Legacy Compatibility", test_legacy_compatibility),
        ("No Permanent Local Storage", test_no_permanent_local_storage),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"🧪 Running {test_name} test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED\n")
            else:
                failed += 1
                print(f"❌ {test_name} test FAILED\n")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} test CRASHED: {e}\n")
    
    print("=" * 60)
    print(f"🏁 Verification Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n📋 Clean Architecture Summary:")
        print("✅ Temporary business data properly separated from persistent configuration")
        print("✅ Automatic cleanup mechanisms implemented")
        print("✅ Firebase-first data management established")
        print("✅ Legacy compatibility maintained")
        print("✅ No permanent local storage for business data")
        print("✅ Professional naming conventions implemented")
        print("\n🚀 The Wiz-Aroma system now follows clean architecture principles!")
        return True
    else:
        print(f"\n❌ {failed} tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
