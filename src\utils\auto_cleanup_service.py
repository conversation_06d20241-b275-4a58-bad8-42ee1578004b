"""
Automatic Cleanup Service for Wiz-Aroma System
Implements automatic cleanup of temporary business data after order completion/cancellation
while preserving essential configuration data and initialization mechanisms.
"""

import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from src.config import logger
from src.firebase_db import get_data, delete_data
from src.utils.temp_data_manager import (
    cleanup_business_data_after_order_completion,
    cleanup_user_session_data,
    force_cleanup_all_temporary_business_data
)


class AutoCleanupService:
    """
    Automatic cleanup service that monitors and cleans up temporary business data
    while preserving configuration data and system initialization components.
    """
    
    def __init__(self):
        self._cleanup_thread: Optional[threading.Thread] = None
        self._running = False
        self._lock = threading.Lock()
        
        # Cleanup intervals (in seconds)
        self.order_cleanup_delay = 60  # 1 minute after order completion
        self.session_timeout = 1800  # 30 minutes for user sessions
        self.stale_data_cleanup_interval = 300  # 5 minutes
        self.force_cleanup_interval = 3600  # 1 hour for force cleanup
        
        # Tracking
        self._last_force_cleanup = time.time()
        self._cleanup_stats = {
            'orders_cleaned': 0,
            'sessions_cleaned': 0,
            'stale_data_cleaned': 0,
            'last_cleanup': None
        }
    
    def start_service(self):
        """Start the automatic cleanup service"""
        with self._lock:
            if self._running:
                logger.warning("Auto cleanup service is already running")
                return
            
            self._running = True
            self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
            self._cleanup_thread.start()
            
        logger.info("🧹 Auto cleanup service started")
    
    def stop_service(self):
        """Stop the automatic cleanup service"""
        with self._lock:
            self._running = False
            
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=5)
            
        logger.info("🛑 Auto cleanup service stopped")
    
    def _cleanup_loop(self):
        """Main cleanup loop that runs in background thread"""
        logger.info("🔄 Auto cleanup loop started")
        
        while self._running:
            try:
                # Perform different types of cleanup
                self._cleanup_completed_orders()
                self._cleanup_expired_sessions()
                self._cleanup_stale_admin_reviews()
                self._cleanup_orphaned_delivery_assignments()
                
                # Periodic force cleanup to ensure no data accumulates
                if time.time() - self._last_force_cleanup > self.force_cleanup_interval:
                    self._perform_force_cleanup()
                    self._last_force_cleanup = time.time()
                
                # Update stats
                self._cleanup_stats['last_cleanup'] = datetime.now().isoformat()
                
                # Sleep before next cleanup cycle
                time.sleep(self.stale_data_cleanup_interval)
                
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                time.sleep(30)  # Wait before retrying
    
    def _cleanup_completed_orders(self):
        """Clean up temporary data for completed orders"""
        try:
            # Get completed orders from Firebase
            completed_orders = get_data("completed_orders") or {}
            cancelled_orders = get_data("cancelled_orders") or {}
            
            all_finished_orders = {**completed_orders, **cancelled_orders}
            
            # Check for orders completed more than cleanup_delay ago
            current_time = datetime.now()
            orders_to_cleanup = []
            
            for order_number, order_data in all_finished_orders.items():
                completed_at = order_data.get('completed_at') or order_data.get('cancelled_at')
                if completed_at:
                    try:
                        completed_time = datetime.fromisoformat(completed_at.replace('Z', '+00:00'))
                        if (current_time - completed_time).total_seconds() > self.order_cleanup_delay:
                            orders_to_cleanup.append((order_number, order_data.get('user_id')))
                    except ValueError:
                        # Invalid timestamp, clean up anyway
                        orders_to_cleanup.append((order_number, order_data.get('user_id')))
            
            # Clean up identified orders
            for order_number, user_id in orders_to_cleanup:
                if user_id:
                    cleanup_business_data_after_order_completion(order_number, user_id)
                    self._cleanup_stats['orders_cleaned'] += 1
            
            if orders_to_cleanup:
                logger.info(f"🧹 Cleaned up {len(orders_to_cleanup)} completed orders")
                
        except Exception as e:
            logger.error(f"Error cleaning up completed orders: {e}")
    
    def _cleanup_expired_sessions(self):
        """Clean up expired user sessions"""
        try:
            from src.data_models import temp_order_status, temp_orders
            
            current_time = time.time()
            expired_users = []
            
            # Check for expired user sessions based on order status timestamps
            for user_id_str in list(temp_order_status.keys()):
                # If user has been in same status for too long, consider session expired
                # This is a simple heuristic - in production you might want more sophisticated tracking
                try:
                    user_id = int(user_id_str)
                    
                    # Check if user has an active order
                    if user_id_str not in temp_orders:
                        # No active order, session might be stale
                        expired_users.append(user_id)
                        
                except ValueError:
                    # Invalid user ID format
                    expired_users.append(user_id_str)
            
            # Clean up expired sessions
            for user_id in expired_users:
                cleanup_user_session_data(user_id)
                self._cleanup_stats['sessions_cleaned'] += 1
            
            if expired_users:
                logger.info(f"🧹 Cleaned up {len(expired_users)} expired user sessions")
                
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {e}")
    
    def _cleanup_stale_admin_reviews(self):
        """Clean up stale admin reviews that are no longer in Firebase"""
        try:
            from src.data_models import temp_pending_admin_reviews
            
            # Get current pending reviews from Firebase
            firebase_reviews = get_data("pending_admin_reviews") or {}
            
            # Find reviews in local cache that are not in Firebase
            stale_reviews = []
            for order_number in list(temp_pending_admin_reviews.keys()):
                if order_number not in firebase_reviews:
                    stale_reviews.append(order_number)
            
            # Clean up stale reviews
            for order_number in stale_reviews:
                del temp_pending_admin_reviews[order_number]
                self._cleanup_stats['stale_data_cleaned'] += 1
            
            if stale_reviews:
                logger.info(f"🧹 Cleaned up {len(stale_reviews)} stale admin reviews")
                
        except Exception as e:
            logger.error(f"Error cleaning up stale admin reviews: {e}")
    
    def _cleanup_orphaned_delivery_assignments(self):
        """Clean up orphaned delivery assignments"""
        try:
            from src.data_models import temp_delivery_personnel_assignments
            
            # Get active orders from Firebase
            active_orders = get_data("active_orders") or {}
            active_order_numbers = set(active_orders.keys())
            
            # Find assignments for orders that no longer exist
            orphaned_assignments = []
            for assignment_id, assignment_data in list(temp_delivery_personnel_assignments.items()):
                order_number = assignment_data.get('order_number')
                if order_number and order_number not in active_order_numbers:
                    orphaned_assignments.append(assignment_id)
            
            # Clean up orphaned assignments
            for assignment_id in orphaned_assignments:
                del temp_delivery_personnel_assignments[assignment_id]
                self._cleanup_stats['stale_data_cleaned'] += 1
            
            if orphaned_assignments:
                logger.info(f"🧹 Cleaned up {len(orphaned_assignments)} orphaned delivery assignments")
                
        except Exception as e:
            logger.error(f"Error cleaning up orphaned delivery assignments: {e}")
    
    def _perform_force_cleanup(self):
        """Perform force cleanup to ensure no data accumulates"""
        try:
            cleaned_count = force_cleanup_all_temporary_business_data()
            if cleaned_count > 0:
                logger.warning(f"🧹 FORCE CLEANUP: Removed {cleaned_count} temporary data entries")
                self._cleanup_stats['stale_data_cleaned'] += cleaned_count
            else:
                logger.debug("🧹 Force cleanup: No temporary data to clean")
                
        except Exception as e:
            logger.error(f"Error in force cleanup: {e}")
    
    def get_cleanup_stats(self) -> Dict[str, Any]:
        """Get cleanup statistics"""
        return self._cleanup_stats.copy()
    
    def manual_cleanup_order(self, order_number: str, user_id: int) -> bool:
        """
        Manually trigger cleanup for a specific order.
        
        Args:
            order_number: Order number to clean up
            user_id: User ID associated with the order
            
        Returns:
            True if cleanup successful
        """
        try:
            cleanup_business_data_after_order_completion(order_number, user_id)
            self._cleanup_stats['orders_cleaned'] += 1
            logger.info(f"🧹 Manual cleanup completed for order {order_number}")
            return True
            
        except Exception as e:
            logger.error(f"Error in manual cleanup for order {order_number}: {e}")
            return False
    
    def manual_cleanup_user_session(self, user_id: int) -> bool:
        """
        Manually trigger cleanup for a specific user session.
        
        Args:
            user_id: User ID to clean up
            
        Returns:
            True if cleanup successful
        """
        try:
            cleanup_user_session_data(user_id)
            self._cleanup_stats['sessions_cleaned'] += 1
            logger.info(f"🧹 Manual session cleanup completed for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error in manual session cleanup for user {user_id}: {e}")
            return False


# Global instance
auto_cleanup_service = AutoCleanupService()


def initialize_auto_cleanup_service():
    """Initialize and start the automatic cleanup service"""
    auto_cleanup_service.start_service()
    logger.info("🧹 Auto cleanup service initialized")


def shutdown_auto_cleanup_service():
    """Shutdown the automatic cleanup service"""
    auto_cleanup_service.stop_service()
    logger.info("🛑 Auto cleanup service shutdown")


def get_auto_cleanup_service() -> AutoCleanupService:
    """Get the global auto cleanup service instance"""
    return auto_cleanup_service
