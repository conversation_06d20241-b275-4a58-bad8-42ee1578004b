# 🏗️ **Wiz Aroma Food Delivery System v2.0 Architecture**

## **Enterprise-Grade Multi-Bot Telegram Delivery Management Platform**

---

## 📊 **Architecture Overview**

```
┌─────────────────────────────────────────────────────────────────┐
│                    🌐 TELEGRAM BOT API                         │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    🤖 MULTI-BOT LAYER v2.0                     │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│ 🛍️ User Bot │ 📊 Mgmt Bot │ 🚚 Delivery │ 📋 Track   │ 🔧 Maint│
│ (Customer)  │ (Analytics) │    Bot      │   Bot       │   Bot   │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    ⚙️ BUSINESS LOGIC LAYER                     │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│ Broadcast   │ Point-Based │ Personnel   │ Firebase    │ Order   │
│ Assignment  │ Payments    │ Management  │ Operations  │ Lifecycle│
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    🗄️ DATA LAYER v2.0                         │
├─────────────────────────────────────────────────────────────────┤
│ 🔥 Firebase Firestore & Realtime Database (EXCLUSIVE)          │
│ • All data stored and accessed exclusively from Firebase       │
│ • No local fallbacks or cache dependencies                     │
│ • Real-time synchronization across all components              │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🤖 **Bot Architecture Details v2.1**

### **🛍️ User Bot**

**Primary customer-facing interface with consolidated messaging system**

**Key Features:**

- Firebase-first data access with automatic cleanup for all customer operations
- Consolidated messaging with single message updates (editing instead of multiple messages)
- Point-based payment system with loyalty rewards (10 + 1% of delivery fee)
- Complete order lifecycle with customer confirmation system
- Favorite orders stored and accessed from Firebase
- Multiple payment methods: Telebirr, CBE Bank, BOA Bank, Points
- Temporary order data automatically cleaned after completion

**Core Functions:**

- Order placement with real-time menu data from Firebase
- Payment processing with point redemption capabilities
- Order tracking with consolidated status updates
- Customer support and help system

### **📊 Management Bot**

**Comprehensive management interface replacing notification bot functionality**

**Key Features:**

- Personnel management with 50% delivery fee sharing (floor rounding)
- Real-time analytics and reporting from Firebase data
- Delivery personnel CRUD operations with Firebase storage
- Weekly earnings reset automation (every Monday)
- Seasonal data reset with multi-step confirmation
- ID-based selection workflows for cleaner interfaces

**Core Functions:**

- Add/edit/delete delivery personnel with Firestore document IDs
- Analytics dashboard with revenue/profit tracking
- Time-based reporting (daily/weekly/monthly/all-time)
- Payroll system with automatic earnings calculations
- Data management and system maintenance

### **🚚 Delivery Bot**

**Broadcast-based order assignment system**

**Key Features:**

- Broadcast orders to all available delivery personnel (<5 orders)
- First-come-first-served order acceptance logic
- Automatic message cleanup when orders are accepted
- 5-order limit per delivery personnel for optimal workload
- Privacy-protected notifications (hide customer details, use restaurant phone)
- Enhanced order details with proper location formatting

**Core Functions:**

- Real-time order broadcasting with Accept/Decline buttons
- Order completion tracking with delivery personnel actions
- Capacity-based assignment (personnel with <5 orders only)
- Automatic cleanup of broadcast messages after acceptance

### **📋 Order Tracking Bot**

**Internal order monitoring and customer contact system**

**Key Features:**

- Complete Order ID display in format: TGID_YYMMDDHHSS_ORDERNUMBER
- Customer contact functionality for delivery issues
- 'Confirm Order Received' and 'Contact Customer' buttons
- Timeout logic for unconfirmed deliveries
- Administrative access restrictions

**Core Functions:**

- Real-time order status monitoring
- Customer contact interface for delivery team
- Order confirmation tracking
- Issue resolution workflow

### **🔧 Maintenance Bot**

**System configuration and maintenance interface**

**Key Features:**

- Firebase data configuration and management
- System health monitoring and diagnostics
- Data integrity checks and repairs
- Configuration updates for areas, restaurants, menus

**Core Functions:**

- System configuration management
- Data maintenance and cleanup operations
- Health checks and system diagnostics
- Emergency system controls

---

## 🔄 **Data Flow Architecture v2.0**

### **Firebase-Exclusive Data Storage**

The system uses **Firebase Firestore and Realtime Database exclusively** with no local fallbacks:

1. **Customer Interaction**: Users interact with the User Bot to place orders
2. **Firebase Storage**: All data (orders, user info, menus, personnel) stored in Firebase
3. **Broadcast Assignment**: Orders broadcast to available delivery personnel via Delivery Bot
4. **Real-time Updates**: All bots access live data from Firebase with real-time synchronization
5. **Order Lifecycle**: Complete tracking from placement → assignment → delivery → confirmation
6. **Analytics**: Management Bot provides real-time analytics from Firebase data

### **Key Data Collections in Firebase**

- **`orders/`** - All order data with complete lifecycle tracking
- **`delivery_personnel/`** - Personnel management with earnings and capacity tracking
- **`user_data/`** - Customer information, points, and preferences
- **`restaurants/`** - Restaurant and menu data
- **`analytics/`** - Real-time system performance metrics
- **`system_config/`** - Dynamic system configuration and settings

### **Payment Flow with Points System**

1. **Order Placement**: Customer selects items and delivery location
2. **Payment Choice**: Customer chooses between cash payment or points redemption
3. **Points Calculation**: System calculates points earned (10 + 1% of delivery fee)
4. **Fee Sharing**: 50% of delivery fees shared with personnel (floor rounding)
5. **Firebase Update**: All payment data stored in Firebase with real-time sync

- Tracking transactions
- Processing refunds
- **Access**: Restricted to authorized finance users (configured in `.env`)

### Maintenance Bot (`@Wiz_Aroma_Maintenance_bot`)

- **Purpose**: Handles system configuration and maintenance
- **Features**:
  - Managing areas
  - Managing restaurants
  - Managing menus
  - Managing delivery locations and fees
  - System configuration
- **Access**: Restricted to authorized maintenance users (configured in `.env`)

## Data Flow

The system uses a combination of in-memory data and persistent storage:

1. **User Interaction**: Users interact with the User Bot to place orders
2. **Order Processing**: Orders are stored in memory and persisted to JSON files
3. **Admin Review**: Admin Bot receives new orders for review
4. **Payment Processing**: After admin approval, users submit payment through the User Bot
5. **Finance Verification**: Finance Bot receives payment receipts for verification
6. **Order Fulfillment**: After payment verification, the order is processed for delivery

---

## 🏗️ **Data Architecture v2.1**

### **🔥 Firebase-First Data Management**

The system implements a clean, professional data architecture with clear separation of concerns:

#### **📊 Data Classification System**

```
📁 TEMPORARY BUSINESS DATA (temp_*)
├── temp_orders                    # Active orders during processing
├── temp_order_status             # Order status during processing
├── temp_pending_admin_reviews     # Admin reviews during processing
├── temp_admin_remarks             # Admin remarks during processing
├── temp_awaiting_receipt          # Payment receipts during processing
├── temp_delivery_locations        # Delivery locations during processing
├── temp_current_order_numbers     # Order numbers during processing
├── temp_delivery_personnel_assignments  # Active delivery assignments
├── temp_delivery_personnel_availability # Personnel availability status
└── temp_delivery_personnel_capacity     # Personnel capacity tracking

📁 PERSISTENT USER DATA (Firebase-Only)
├── user_points                    # User points balances
├── user_names                     # User names
├── user_phone_numbers            # User phone numbers
├── user_emails                   # User email addresses
├── user_order_history            # Complete order history
├── favorite_orders               # User favorite orders
├── delivery_personnel            # Delivery personnel data
├── delivery_personnel_earnings   # Personnel earnings tracking
└── delivery_personnel_performance # Performance metrics

📁 STATIC CONFIGURATION DATA (config_*)
├── config_areas_data             # Areas configuration (cached locally)
├── config_restaurants_data       # Restaurants configuration (cached locally)
├── config_menus_data            # Menus configuration (cached locally)
├── config_delivery_locations_data # Delivery locations config (cached locally)
└── config_delivery_fees_data     # Delivery fees configuration (cached locally)
```

#### **🧹 Automatic Data Lifecycle Management**

- **Order Completion Cleanup**: All temporary data automatically removed after order completion
- **Session Timeout Handling**: 30-minute user session cleanup with automatic data removal
- **Stale Data Detection**: Orphaned data automatically identified and cleaned
- **Force Cleanup Service**: Hourly comprehensive cleanup to prevent accumulation
- **Background Service**: Continuous monitoring and cleanup of temporary data

#### **⚡ Professional Code Organization**

- **Standardized Naming**: `temp_*`, `config_*`, `firebase_*` conventions
- **Clean Architecture**: Proper separation between business logic and data storage
- **Enterprise Patterns**: Professional data management throughout the codebase
- **Backward Compatibility**: Smooth transition with legacy support maintained

---

## Component Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│    User Bot     │◄────┤    Admin Bot    │◄────┤   Finance Bot   │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                       Shared Data Storage                       │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
         ▲
         │
         │
┌────────┴────────┐
│                 │
│ Maintenance Bot │
│                 │
└─────────────────┘
```

## Data Storage

The system uses JSON files for persistent storage:

- **User Data**:
  - `user_points.json`: User points balance
  - `user_names.json`: User names
  - `user_phone_numbers.json`: User phone numbers
  - `user_order_history.json`: User order history
  - `favorite_orders.json`: User favorite orders

- **Order Data**:
  - `current_orders.json`: Current active orders
  - `order_status.json`: Status of orders in progress
  - `pending_admin_reviews.json`: Orders waiting for admin review
  - `admin_remarks.json`: Admin remarks on orders
  - `awaiting_receipt.json`: Orders waiting for payment receipt

- **Configuration Data**:
  - `areas.json`: Available areas
  - `restaurants.json`: Available restaurants
  - `menus.json`: Restaurant menus
  - `delivery_locations.json`: Available delivery locations
  - `delivery_fees.json`: Delivery fees for different locations

## Process Flow

1. **Order Placement**:
   - User selects area
   - User selects restaurant
   - User adds menu items
   - User provides delivery details
   - User confirms order

2. **Admin Review**:
   - Admin receives order notification
   - Admin reviews order details
   - Admin approves or rejects order
   - Admin can add remarks if needed

3. **Payment Processing**:
   - User receives approval notification
   - User selects payment method
   - User sends payment receipt
   - Finance receives payment receipt
   - Finance verifies payment

4. **Order Fulfillment**:
   - User receives payment confirmation
   - Order is processed for delivery
   - User can track order status

## Error Handling

The system includes comprehensive error handling:

- **Exception Handling**: All operations are wrapped in try-except blocks
- **Logging**: Detailed logging of all operations and errors
- **Data Consistency**: Regular checks to ensure data consistency
- **Backup**: Automatic backup of data files before writing

## Security

- **Authentication**: Only authorized users can access admin, finance, and maintenance bots
- **Environment Variables**: All sensitive information is stored in environment variables
- **Input Validation**: All user inputs are validated before processing
